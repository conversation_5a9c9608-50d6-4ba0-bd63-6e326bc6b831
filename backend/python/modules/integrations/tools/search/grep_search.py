import os
import re
from typing import List, Tuple, Optional
import subprocess
import xml.etree.ElementTree as ET
import fnmatch
import asyncio
from pathlib import Path

from modules.common.schema import CodeSnippet
from modules.integrations.tools.search.search_tool import SearchToolABC
from core.config import get_config
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

from utils.file import should_ignore_path

class GrepSearchTool(SearchToolABC):
    """基于ripgrep的搜索引擎"""
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.file_filter = get_config().file_filter



    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用ripgrep搜索代码片段，支持XML格式的查询参数

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 构建ripgrep命令
            cmd = self._build_ripgrep_command(search_params)
            logger.info(f"CMD: {cmd}")

            # 执行搜索
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                snippets = self._parse_ripgrep_output(result.stdout, search_params)
            elif result.returncode == 1:
                # ripgrep返回1表示没有找到匹配，这是正常情况
                logger.info("No matches found")
            else:
                logger.warning(f"Ripgrep command failed with return code {result.returncode}: {result.stderr}")

        except Exception as e:
            logger.error(f"Ripgrep search failed: {e}")

        return snippets

    async def search_async(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用ripgrep搜索代码片段的异步版本

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 构建ripgrep命令
            cmd = self._build_ripgrep_command(search_params)
            logger.info(f"CMD: {cmd}")

            # 异步执行索搜
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                # 将bytes解码为字符串
                stdout_str = stdout.decode('utf-8', errors='ignore')
                snippets = self._parse_ripgrep_output(stdout_str, search_params)
            elif process.returncode == 1:
                # ripgrep返回1表示没有找到匹配，这是正常情况
                logger.info("No matches found")
            else:
                # 将stderr解码为字符串用于日志记录
                stderr_str = stderr.decode('utf-8', errors='ignore')
                logger.warning(f"Ripgrep command failed with return code {process.returncode}: {stderr_str}")

        except Exception as e:
            logger.error(f"Async ripgrep search failed: {e}")

        return snippets

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'path': '.',  # 默认为当前目录，相对于repo_path
            'regex': query.strip(),
            'file_pattern': None
        }

        # 尝试解析XML格式
        query_stripped = query.strip()

        try:
            # 解析XML
            root = ET.fromstring(query_stripped)

            # 提取参数
            path_elem = root.find('path')
            if path_elem is not None and path_elem.text:
                # 处理相对路径
                path = path_elem.text.strip()
                params['path'] = path  # 保存原始路径，在build_command中处理

            regex_elem = root.find('regex')
            if regex_elem is not None and regex_elem.text:
                params['regex'] = regex_elem.text.strip()

            file_pattern_elem = root.find('file_pattern')
            if file_pattern_elem is not None and file_pattern_elem.text:
                params['file_pattern'] = file_pattern_elem.text.strip()

            logger.debug(f"Successfully parsed XML query: path={params['path']}, regex={params['regex']}, file_pattern={params['file_pattern']}")

        except ET.ParseError as e:
            logger.warning(f"Failed to parse XML query, using as plain text: {e}")
            logger.debug(f"Failed XML content: {query_stripped}")

        return params

    def _build_ripgrep_command(self, params: dict) -> List[str]:
        """
        构建ripgrep命令

        Args:
            params: 搜索参数

        Returns:
            List[str]: ripgrep命令参数列表
        """
        cmd = [
            "rg",
            "--line-number",  # 显示行号
            "--context", "3",  # 显示上下文3行
            "--color", "never",  # 不使用颜色输出
        ]

        # 处理正则表达式中的特殊标志
        regex = params['regex']

        # 检测是否需要多行匹配
        if self._needs_multiline_matching(regex):
            cmd.append('--multiline')  # 启用多行匹配
            regex = self._convert_to_multiline_regex(regex)  # 转换正则表达式

        # 处理大小写不敏感标志
        if regex.startswith('(?i)'):
            cmd.append('--ignore-case')  # 忽略大小写
            regex = regex[4:]  # 移除(?i)前缀

        # 处理搜索路径
        search_path = self._infer_file_path_from_search_params(params)

        # 添加文件模式过滤
        if params['file_pattern']:
            cmd.extend(["--glob", params['file_pattern']])
        elif os.path.isfile(search_path):
            # 如果是具体文件，不需要添加glob过滤
            pass
        else:
            # 只有在搜索目录时才使用配置文件中的文件类型过滤
            for ext in self.file_filter.local_include:
                cmd.extend(["--glob", f"*{ext}"])

        # 添加正则表达式和搜索路径
        cmd.append(regex)
        cmd.append(search_path)

        return cmd

    def _needs_multiline_matching(self, regex: str) -> bool:
        """
        检测正则表达式是否需要多行匹配

        Args:
            regex: 正则表达式字符串

        Returns:
            bool: 如果需要多行匹配返回True
        """
        # 检测可能需要多行匹配的模式
        multiline_indicators = [
            r'\\n',                                    # 字面量换行符
            r'.*?(?:return|pass|break|continue)',      # 函数体到控制语句的匹配
            r'def\s+\w+.*?:.*?(?:return|pass)',        # 函数定义到return的匹配
            r'class\s+\w+.*?:.*?def',                  # 类定义到方法定义
            r'""".*?"""',                              # 多行文档字符串
            r"'''.*?'''",                              # 多行文档字符串（单引号）
            r'{\s*.*?\s*}',                            # 可能跨行的代码块
        ]

        for pattern in multiline_indicators:
            if re.search(pattern, regex):
                return True

        return False

    def _convert_to_multiline_regex(self, regex: str) -> str:
        """
        将正则表达式转换为适合多行匹配的格式

        Args:
            regex: 原始正则表达式

        Returns:
            str: 转换后的多行正则表达式
        """
        # 将字面量 \n 替换为适合多行匹配的模式
        multiline_regex = regex.replace(r'\n', r'[\s\S]')
        # 将 .*? 替换为 [\s\S]*? 以匹配包括换行符在内的任何字符
        multiline_regex = multiline_regex.replace('.*?', r'[\s\S]*?')
        return multiline_regex

    def _parse_ripgrep_output(self, output: str, search_params: dict) -> List[CodeSnippet]:
        """
        解析ripgrep输出结果，基于ripgrep的自然分组

        Args:
            output: ripgrep命令输出
            search_params: 搜索参数，用于推断文件路径

        Returns:
            List[CodeSnippet]: 解析后的代码片段
        """
        snippets = []

        # 按ripgrep的分隔符（--）分割成独立的块
        blocks = re.split(r'\n--\n', output.strip())

        for block in blocks:
            if not block.strip():
                continue

            current_file = None
            current_lines = []

            for line in block.split('\n'):
                if not line.strip():
                    continue

                # 匹配行格式：可能包含文件路径，也可能不包含
                # 格式1：/path/to/file.py:157:    def executemany(self, query, args):
                # 格式2：157:    def executemany(self, query, args): (单文件搜索时)
                match = re.match(r'^([^:]+):(\d+):(.*)$', line)
                context_match = re.match(r'^([^:]+)-(\d+)-(.*)$', line)

                # 单文件搜索时的格式：行号:内容 或 行号-内容
                simple_match = re.match(r'^(\d+):(.*)$', line)
                simple_context_match = re.match(r'^(\d+)-(.*)$', line)

                if match:
                    file_path, line_number, content = match.groups()
                    if not should_ignore_path(file_path):
                        current_file = file_path
                        current_lines.append((int(line_number), content, True))
                elif context_match:
                    file_path, line_number, content = context_match.groups()
                    if not should_ignore_path(file_path):
                        if current_file is None:
                            current_file = file_path
                        if current_file == file_path:
                            current_lines.append((int(line_number), content, False))
                elif simple_match:
                    # 单文件搜索格式，需要从搜索参数中获取文件路径
                    line_number, content = simple_match.groups()
                    if current_file is None:
                        # 从搜索路径推断文件路径
                        current_file = self._infer_file_path_from_search_params(search_params)
                    current_lines.append((int(line_number), content, True))
                elif simple_context_match:
                    line_number, content = simple_context_match.groups()
                    if current_file is None:
                        current_file = self._infer_file_path_from_search_params(search_params)
                    current_lines.append((int(line_number), content, False))

            # 处理当前块
            if current_file and current_lines:
                snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))

        return snippets
    
    def _extract_snippets_from_lines(self, file_path: str, lines: List[Tuple[int, str, bool]]) -> List[CodeSnippet]:
        """
        从行数据中提取代码片段，智能合并相近的匹配

        Args:
            file_path: 文件路径
            lines: 行数据列表 (行号, 内容, 是否匹配行)

        Returns:
            List[CodeSnippet]: 代码片段列表
        """
        snippets = []

        if not lines:
            return snippets

        # 按行号排序，确保顺序正确
        lines.sort(key=lambda x: x[0])

        # 找到所有匹配行
        match_indices = [i for i, (_, _, is_match) in enumerate(lines) if is_match]

        if not match_indices:
            return snippets

        # ripgrep已经为我们做了自然分组，直接处理所有匹配行作为一个组
        snippet = self._create_snippet_from_group(file_path, lines, match_indices)
        if snippet:
            snippets.append(snippet)

        return snippets

    def _infer_file_path_from_search_params(self, search_params: dict) -> str:
        """
        从搜索参数中推断文件路径

        Args:
            search_params: 搜索参数

        Returns:
            str: 推断的文件路径
        """
        search_path = search_params['path']
        if search_path == '.':
            return self.repo_path
        elif os.path.isabs(search_path):
            return search_path
        else:
            return os.path.join(self.repo_path, search_path)

    def _create_snippet_from_group(self, file_path: str, lines: List[Tuple[int, str, bool]], group: List[int]) -> Optional[CodeSnippet]:
        """
        从一组匹配行创建代码片段，统一处理单个和多个匹配的情况

        Args:
            file_path: 文件路径
            lines: 行数据列表
            group: 匹配行索引组

        Returns:
            Optional[CodeSnippet]: 代码片段，如果无法创建则返回None
        """
        if not group:
            return None

        # 收集匹配行信息
        match_lines = [(lines[idx][0], lines[idx][1]) for idx in group]  # (line_num, content)
        first_match_line = match_lines[0][0]
        last_match_line = match_lines[-1][0]

        # 收集前置上下文（第一个匹配行之前的上下文）
        context_before = []
        context_before_lines = []
        for i in range(group[0] - 1, -1, -1):
            line_num, content, is_match = lines[i]
            if not is_match:
                context_before.insert(0, content)
                context_before_lines.insert(0, line_num)
            else:
                break

        # 收集后置上下文（最后一个匹配行之后的上下文）
        context_after = []
        context_after_lines = []
        for i in range(group[-1] + 1, len(lines)):
            line_num, content, is_match = lines[i]
            if not is_match:
                context_after.append(content)
                context_after_lines.append(line_num)
            else:
                break

        # 计算完整的行号范围
        start_line = context_before_lines[0] if context_before_lines else first_match_line
        end_line = context_after_lines[-1] if context_after_lines else last_match_line

        # 构建匹配内容
        if len(match_lines) == 1:
            # 单个匹配，直接使用内容
            content = match_lines[0][1]
        else:
            # 多个匹配，格式化显示
            content = "\n".join([f"Line {line_num}: {content}" for line_num, content in match_lines])

        return CodeSnippet(
            file_path=os.path.relpath(file_path, self.repo_path),
            start_line=start_line,
            end_line=end_line,
            content=content,
            context_before="\n".join(context_before),
            context_after="\n".join(context_after)
        )

    @property
    def description(self):
        return """-`grep`: Universal regex-based code search tool powered by ripgrep. Searches through files in any programming language and returns matches with context. Supports both simple patterns and complex multi-line regex patterns.
**Parameters:**
- `path`: (required) Directory or file path to search. Examples:
  - `src/components` - search in specific directory
  - `src/utils/helper.py` - search in specific file
  - `.` - search in current directory

- `regex`: (required) Regular expression pattern. **LANGUAGE-SPECIFIC PATTERNS:**

  **Python:**
  - Functions: def function_name\\s*\\([^)]*\\):
  - Classes: class ClassName\\s*\\([^)]*\\):
  - Imports: from module import|import module

  **JavaScript/TypeScript:**
  - Functions: function\\s+\\w+\\s*\\(|const\\s+\\w+\\s*=\\s*\\(|\\w+\\s*=>
  - Classes: class\\s+\\w+\\s*{
  - Imports: import.*from|require\\(

  **Java/C#:**
  - Methods: (public|private|protected).*\\w+\\s*\\([^)]*\\)\\s*{
  - Classes: (public\\s+)?class\\s+\\w+
  - Interfaces: interface\\s+\\w+

  **C/C++:**
  - Functions: \\w+\\s+\\w+\\s*\\([^)]*\\)\\s*{
  - Structs: struct\\s+\\w+\\s*{
  - Headers: #include\\s*[<\"]

  **Go:**
  - Functions: func\\s+\\w+\\s*\\([^)]*\\)
  - Structs: type\\s+\\w+\\s+struct\\s*{
  - Packages: package\\s+\\w+

  **Rust:**
  - Functions: fn\\s+\\w+\\s*\\([^)]*\\)
  - Structs: struct\\s+\\w+\\s*{
  - Traits: trait\\s+\\w+

  **Multi-line patterns (automatically detected):**
  - Function with body: func_pattern.*?{.*?}
  - Class with methods: class_pattern.*?{.*?}
  - Documentation blocks: /\\*\\*.*?\\*/|\"\"\".*?\"\"\"|///.*

  **Escaping rules:**
  - Use \\\\s for whitespace, \\\\w for word chars, \\\\d for digits
  - Use \\\\( and \\\\) for literal parentheses
  - Use \\\\{ and \\\\} for literal braces
  - Use \\\\. for literal dots

- `file_pattern`: (optional) File filter by language:
  - `*.py` - Python files
  - `*.{js,ts,jsx,tsx}` - JavaScript/TypeScript
  - `*.{java,kt}` - Java/Kotlin
  - `*.{c,cpp,h,hpp}` - C/C++
  - `*.{go,mod}` - Go files
  - `*.rs` - Rust files
  - `*.{php,rb,swift}` - Other languages

**Performance Tips:**
- Use specific paths instead of searching entire repositories
- Use file_pattern to limit search scope by language
- Prefer simple patterns over complex multi-line regex when possible"""
    
    @property
    def examples(self):
        return """
<output>
    <grep>
    <path>src</path>
    <regex>def \\w+\\s*\\([^)]*\\):</regex>
    <file_pattern>*.py</file_pattern>
    </grep>

    <grep>
    <path>src/components</path>
    <regex>function\\s+\\w+\\s*\\(|const\\s+\\w+\\s*=\\s*\\(</regex>
    <file_pattern>*.{js,ts,jsx,tsx}</file_pattern>
    </grep>

    <grep>
    <path>src/main/java</path>
    <regex>public\\s+\\w+\\s+\\w+\\s*\\([^)]*\\)\\s*\\{</regex>
    <file_pattern>*.java</file_pattern>
    </grep>
</output>
```"""

if __name__ == "__main__":
    # 测试用例
    grep_search_tool = GrepSearchTool("/Users/<USER>/01-Projects/Codebase-Dev/backend/python/data/repos/PyMySQL")

    # 测试1: 简单正则表达式
    print("=== 测试1: 简单正则表达式 ===")
    query1 = "<grep><path>pymysql</path><regex>def executemany</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query1}")

    snippets1 = grep_search_tool.search(query1)
    print(f"找到 {len(snippets1)} 个结果")

    if snippets1:
        print("第一个结果:")
        print(f"文件: {snippets1[0].file_path}")
        print(f"行号: {snippets1[0].start_line}")
        print(f"内容: {snippets1[0].content}")

    print("\n=== 测试2: 复杂正则表达式（非贪婪匹配） ===")
    # 测试2: 复杂正则表达式，ripgrep原生支持
    query2 = "<grep><path>pymysql</path><regex>def\\s+\\w+.*?\\(.*?\\):</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query2}")

    snippets2 = grep_search_tool.search(query2)
    print(f"找到 {len(snippets2)} 个结果")

    if snippets2:
        print("前3个结果:")
        for i, snippet in enumerate(snippets2[:3]):
            print(f"{i+1}. 文件: {snippet.file_path}, 行号: {snippet.start_line}")
            print(f"   内容: {snippet.content}")

    print("\n=== 测试3: 非捕获组 ===")
    # 测试3: 非捕获组，ripgrep原生支持
    query3 = "<grep><path>pymysql</path><regex>(?:class|def)\\s+(\\w+)</regex><file_pattern>*.py</file_pattern></grep>"
    print(f"测试查询: {query3}")

    snippets3 = grep_search_tool.search(query3)
    print(f"找到 {len(snippets3)} 个结果")

    if snippets3:
        print("前3个结果:")
        for i, snippet in enumerate(snippets3[:3]):
            print(f"{i+1}. 文件: {snippet.file_path}, 行号: {snippet.start_line}")
            print(f"   内容: {snippet.content}")
