import logging
import json
import xml.etree.ElementTree as ET
from typing import List
from collections import Counter

from core.config import get_config
from modules.common.schema import CodeSnippet
from modules.integrations.tools.search.search_tool import SearchToolABC
from modules.chunks.chunk_factory import getChunkService
from modules.term.term import get_terms, save_bm25_chunk_terms

from utils.file import generate_file_hash_name
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)


class TermSparseSearch(SearchToolABC):
    def __init__(self, repo_path: str, cache_dir: str = get_config().data.cache_dir, refresh: bool = True):
        self.repo_path = repo_path

        self.k1 = 1.2
        self.k2 = 2.0 # Query中词频的重要程度
        self.b = 0.75

        self.chunk_content = None
        self.term_idf = None
        self.chunks_term_freqs = None
        self.avg_chunk_length = None

        # 加载缓存文件  
        self.term_sparse_file_path = save_bm25_chunk_terms(project_dir = repo_path, 
                                                           cache_dir = cache_dir, 
                                                           project_id = generate_file_hash_name(repo_path), 
                                                           refresh=refresh,
                                                           chunk_splitter=getChunkService(get_config().chunk.name)(),
                                                           k1=self.k1,
                                                           b=self.b
                                                          )
        # 读取缓存文件
        with open(self.term_sparse_file_path, 'r', encoding='utf-8') as f:
            cache_content =  json.load(f)
            self.chunk_content = cache_content['chunk_content']
            self.term_idf = cache_content['term_idf']
            self.chunks_term_freqs = cache_content['chunk_term_freqs']
            self.avg_chunk_length = cache_content['avg_chunk_length']


    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用BM25算法搜索代码片段，支持XML格式的查询参数

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        if not self.chunk_content or not self.term_idf or not self.chunks_term_freqs:
            return []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 获取查询参数
            query_text = search_params['query']
            top_k = search_params['top_k']
            file_type = search_params['file_type']

            # 提取查询词
            query_terms = get_terms(query_text, file_type=file_type)

            if logger.isEnabledFor(logging.DEBUG):
                term_bm25 = {}
                for term, term_count in Counter(query_terms).items():
                    if self.term_idf.get(term, 0) == 0:
                        continue
                    term_tf = (term_count * (self.k2 + 1.0)) / (term_count + self.k2)
                    term_bm25[term] = term_tf * self.term_idf[term]
                # 按排序展示
                term_bm25 = dict(sorted(term_bm25.items(), key=lambda item: item[1], reverse=True))
                # logger.debug(f"Query Terms BM25: {json.dumps(term_bm25, ensure_ascii=False, indent=2)}")

            if not query_terms:
                return []

            # 计算每个文档的匹配分数
            scores = {}
            for chunk_path in self.chunk_content:
                score = self._calculate_term_sparse_score(query_terms, chunk_path)
                if score > 0:
                    scores[chunk_path] = score

            # 按分数排序并返回top_k结果
            sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
            return_count = min(top_k, len(sorted_scores))

            # logger.debug(f"BM25 Search Sorted Scores: {json.dumps(sorted_scores[:return_count], ensure_ascii=False, indent=2)}")

            # 返回结果
            return [CodeSnippet(
                file_path=chunk_path.split(":")[0],
                start_line=int(chunk_path.split(":")[1].split("-")[0]),
                end_line=int(chunk_path.split(":")[1].split("-")[1]),
                content=self.chunk_content[chunk_path],
                context_before="",
                context_after="",
                score=scores[chunk_path]
            ) for chunk_path, _ in sorted_scores[:return_count]]

        except Exception as e:
            logger.error(f"Term sparse search failed: {e}")
            return []

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        支持以下XML格式：
        1. 普通文本: <query>查询内容</query>
        2. CDATA格式: <query><![CDATA[包含<>等特殊字符的查询内容]]></query>
        3. HTML实体转义: <query>包含&lt;等转义字符的查询内容</query>

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 导入FileType
        from utils.file import FileType
        import html

        # 默认参数
        params = {
            'query': query.strip(),
            'top_k': 20,
            'file_type': FileType.CODE
        }

        # 尝试解析XML格式
        if query.strip().startswith('<term_sparse>'):
            try:
                # 预处理：自动转义未转义的特殊字符（但保留已经正确格式化的CDATA和实体）
                processed_query = self._preprocess_xml_query(query.strip())

                # 解析XML
                root = ET.fromstring(processed_query)

                # 提取查询内容
                query_elem = root.find('query')
                if query_elem is not None:
                    if query_elem.text:
                        # 处理普通文本和HTML实体转义
                        params['query'] = html.unescape(query_elem.text.strip())
                    else:
                        # 处理可能的CDATA或混合内容
                        query_content = ''.join(query_elem.itertext()).strip()
                        if query_content:
                            params['query'] = query_content

                # 提取top_k参数
                top_k_elem = root.find('top_k')
                if top_k_elem is not None and top_k_elem.text:
                    try:
                        params['top_k'] = int(top_k_elem.text.strip())
                    except ValueError:
                        logger.warning(f"Invalid top_k value: {top_k_elem.text}, using default 20")

                # 提取file_type参数
                file_type_elem = root.find('file_type')
                if file_type_elem is not None and file_type_elem.text:
                    file_type_str = file_type_elem.text.strip().lower()
                    if file_type_str == 'code':
                        params['file_type'] = FileType.CODE
                    elif file_type_str == 'doc':
                        params['file_type'] = FileType.DOC
                    else:
                        logger.warning(f"Invalid file_type value: {file_type_elem.text}, using default CODE")

            except ET.ParseError as e:
                logger.warning(f"Failed to parse XML query, using as plain text: {e}")

        return params

    def _preprocess_xml_query(self, xml_query: str) -> str:
        """
        预处理XML查询，智能处理特殊字符

        策略：
        1. 如果query标签内容已经是CDATA格式，保持不变
        2. 如果query标签内容已经是HTML实体转义，保持不变
        3. 如果query标签内容包含未转义的<>字符，自动用CDATA包装

        Args:
            xml_query: 原始XML查询字符串

        Returns:
            str: 处理后的XML查询字符串
        """
        import re

        # 查找query标签的内容
        query_pattern = r'<query>(.*?)</query>'
        match = re.search(query_pattern, xml_query, re.DOTALL)

        if not match:
            return xml_query

        query_content = match.group(1)

        # 如果已经是CDATA格式，不处理
        if query_content.strip().startswith('<![CDATA[') and query_content.strip().endswith(']]>'):
            return xml_query

        # 如果包含HTML实体转义，不处理
        if '&lt;' in query_content or '&gt;' in query_content or '&amp;' in query_content:
            return xml_query

        # 如果包含未转义的<>字符，用CDATA包装
        if '<' in query_content or '>' in query_content:
            cdata_content = f'<![CDATA[{query_content}]]>'
            return xml_query.replace(f'<query>{query_content}</query>', f'<query>{cdata_content}</query>')

        return xml_query

    def _calculate_term_sparse_score(self, query_terms: List[str], chunk_path: str) -> float:
        """
        计算单个文档的BM25分数
        """
        if chunk_path not in self.chunks_term_freqs:
            return 0.0
        
        score = 0.0

        for term, term_count in Counter(query_terms).items():
            if term in self.chunks_term_freqs[chunk_path] and term in self.term_idf:
                # logger.info(f"Term: {term}, Chunk: {chunk_path}, TF: {term_freqs[term]}, IDF: {self.term_idf[term]}")
                # 词频
                tf = (term_count * (self.k2 + 1.0)) / (term_count + self.k2)
                
                # IDF值
                idf = self.term_idf[term]

                # BM25公式
                score += idf * idf * tf * self.chunks_term_freqs[chunk_path][term]

        return score

    @property
    def description(self):
        return """`term_sparse`: BM25-based semantic search engine for fuzzy functionality discovery. Designed for scenarios where you don't have specific keywords or need to find code/documentation by describing general functionality or content themes.

**Primary Use Cases**:
1. **No Specific Keywords**: When you don't know exact function names, class names, or technical terms to search for
2. **Functionality-Based Search**: When you want to find code that performs certain functions or documentation covering specific topics, described in general terms

**Key Features**:
- Statistical relevance ranking using BM25 algorithm
- Handles vague or general descriptions by analyzing term patterns
- Works with both code functionality descriptions and documentation content themes
- Returns results ranked by semantic similarity rather than exact matches

**When to Use This Tool**:
- ✅ Use when you want to find chunks with similar term patterns to your query
- ✅ Query should mimic the actual content structure of target chunks
- ✅ For code: use concise pseudo-code that represents the target code structure
- ✅ For docs: use representative text snippets that match documentation style
- ❌ Don't use descriptive language about what you're looking for

**Parameters**:
- query: Content that mimics the structure and terms of target chunks
- file_type: (optional) "code" (default) for source code, "doc" for documentation
- top_k: (optional) Maximum results to return (default: 20)

**Query Style**: Write content that structurally resembles what you expect to find, using similar terms and patterns as the target chunks.
"""

    @property
    def examples(self):
        return """
<output>
    <term_sparse>
    <file_type>code</file_type>
    <query>def authenticate_user(username, password): validate_credentials() check_permissions() return auth_token</query>
    <top_k>15</top_k>
    </term_sparse>

    <term_sparse>
    <file_type>code</file_type>
    <query>connection = database.connect(host, port) cursor = connection.cursor() cursor.execute(sql_query)</query>
    <top_k>20</top_k>
    </term_sparse>

    <!-- 包含特殊字符的查询示例 - 自动CDATA包装 -->
    <term_sparse>
    <file_type>code</file_type>
    <query>def read_packet(self): buffer = bytearray() while len(buffer) < packet_size: chunk = self.sock.recv(min(remaining, buffer_size)) buffer.extend(chunk)</query>
    </term_sparse>

    <!-- 手动CDATA包装示例 -->
    <term_sparse>
    <file_type>code</file_type>
    <query><![CDATA[if condition < threshold: process_data() elif value > max_limit: handle_overflow()]]></query>
    </term_sparse>

    <!-- HTML实体转义示例 -->
    <term_sparse>
    <file_type>code</file_type>
    <query>template = "&lt;div class='item'&gt;{content}&lt;/div&gt;" result = template.format(content=data)</query>
    </term_sparse>

    <term_sparse>
    <file_type>doc</file_type>
    <query>Configuration Setup: Edit config.json file. Set database connection parameters: host, port, username, password. Example configuration:</query>
    </term_sparse>

    <term_sparse>
    <file_type>doc</file_type>
    <query>API Endpoints: GET /api/users - Returns user list. POST /api/users - Creates new user. Response format: {"id": 123, "name": "example"}</query>
    <top_k>10</top_k>
    </term_sparse>
</output>"""

